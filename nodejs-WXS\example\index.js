
const express = require('express');
const server = express();
const bodyParser = require("body-parser");
const port = 7111;
server.use(bodyParser.urlencoded({
    extended: true
}));

// 假设你的 token 和 key 为如下的值
const config = {
    corpId: 'wwd5b8884bc3f43261', // 你的企业ID, 公众号不用,但是解密的思路一样的稍微改下代码,参见 /WXMsgCrypt/pkcs7****.js 第 69 行.
    token: 'b9LENEr8yO2tv', // token
    aes_key: 'iyJ5PLcJvnTcWI7C8CK9bAmPRPTjLX6rLyr02f8qt1D' // key值
}

// 引入加解密库
const WXBizMsgCrypt = require('./../index');

// 实例化加解密库

const wxBizMsgCrypt = new WXBizMsgCrypt(config.token, config.aes_key, config.corpId); // 会自动解构

server.get('/', (req, res) => {
    res.send('Hello World!')
})

// 中间件：记录请求日志
server.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${req.method} ${req.url} - IP: ${req.ip}`);
    next();
});

// GET 请求主要用来验证 url 的，这里只演示企业微信的用法，微信公众号只需要将 明文返回即可，如果需要验证则逻辑和如下相同
server.get('/server', (req, res) => {
    /** 拿到 query 的值并解构如下
     *  msg_signature: '4b1999109ff2a628509b7edcaa460a07bb0f8675',
        timestamp: '1593083194',
        nonce: '1593678893',
        echostr: '7t5l6jS0mub7H/H+SX3sYTmHp8ONrNB9uVKcrVW38XYUIH9YovC0/AILhmtzB9KZoB3whKAM9Iw0FAz5RGU1nw=='
     */
    const { msg_signature, timestamp, nonce, echostr } = req.query;

    const replyEchostrMsg = wxBizMsgCrypt.VerifyURL(msg_signature, timestamp, nonce, echostr); // 会将密文 echostr 解密出来，在返回企业欸新即可

    console.log('replyEchostrMsg',replyEchostrMsg);
    
    res.send(replyEchostrMsg)
})

// 验证URL成功后，企业微信端会以POST的形式，将加密消息发送给我们的服务端
server.post('/server', (req, res) => {

    let data = '';//添加接收变量
    req.setEncoding('utf8');

    req.on('data', function (chunk) { //接收 数据包
        data += chunk;
    });
    req.on('end', function () { // 接受完以后
        // 解密的时候，同样需要拿到 以下 query 参数，不过此时没有了echostr
        const { msg_signature, timestamp, nonce } = req.query;
        // 将接收完的 data 数据包，进行解密
        let recivedMsg = wxBizMsgCrypt.DecryptMsg(msg_signature, timestamp, nonce, data);
 
        console.log('recivedMsg',recivedMsg);
        // res.send(data); // 可以将其原封不动返回去，因为总得返回点什么

        // 假设不论企业微信发个我们什么，我们都回复一样的，仅仅为了测试而已
        // 构建消息体
        const testXmlData = MessageHandle.textXml({
            touser : "2629",
            toparty : "@all",
            totag : "@all",
            msgtype : "text",
            agentid : 1000063,
            text : {
                content : "测试企微api"
            },
            safe:0,
            enable_id_trans: 0,
            enable_duplicate_check: 0,
            duplicate_check_interval: 1800
         })
        // 加密消息体
        let sendReplyMsg = wxBizMsgCrypt.EncryptMsg(testXmlData);
        
        res.send(sendReplyMsg);
    });
})

server.listen(port, () => console.log(`Example app listening on port ${port}!`))

/**
 * @description: 
 * @param {type} 
 * @return: 
 */
class MessageHandle {
    static textXml({ toUser, text }) {
        const sTimeStamp = parseInt(new Date().valueOf() / 1000);
        return {
            sReplyMsg: `<xml><ToUserName><![CDATA[${toUser}]]></ToUserName><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[${text.content}]]></Content></xml>`,
            sTimeStamp
        }
    }
}